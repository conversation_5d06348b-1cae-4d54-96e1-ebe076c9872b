package types

import (
	"encoding/json"
	"strings"
	"time"
)

// FlexibleTime is a custom time type that can parse multiple date formats
type FlexibleTime struct {
	time.Time
}

// UnmarshalJSON implements json.Unmarshaler interface
func (ft *FlexibleTime) UnmarshalJSON(data []byte) error {
	// Remove quotes from JSON string
	str := strings.Trim(string(data), `"`)
	
	if str == "null" || str == "" {
		return nil
	}

	// Try different time formats
	formats := []string{
		"2006-01-02T15:04:05Z07:00", // ISO 8601 with timezone
		"2006-01-02T15:04:05Z",      // ISO 8601 UTC
		"2006-01-02T15:04:05",       // ISO 8601 without timezone
		"2006-01-02 15:04:05",       // SQL datetime format
		"2006-01-02",                // Date only
	}

	var err error
	for _, format := range formats {
		ft.Time, err = time.Parse(format, str)
		if err == nil {
			return nil
		}
	}

	return err
}

// MarshalJSON implements json.Marshaler interface
func (ft FlexibleTime) MarshalJSON() ([]byte, error) {
	if ft.Time.IsZero() {
		return []byte("null"), nil
	}
	return json.Marshal(ft.Time.Format("2006-01-02T15:04:05Z"))
}

// String returns string representation
func (ft FlexibleTime) String() string {
	if ft.Time.IsZero() {
		return ""
	}
	return ft.Time.Format("2006-01-02T15:04:05Z")
}

// ToTimePointer converts to *time.Time
func (ft *FlexibleTime) ToTimePointer() *time.Time {
	if ft == nil || ft.Time.IsZero() {
		return nil
	}
	return &ft.Time
}

// FromTimePointer creates FlexibleTime from *time.Time
func FromTimePointer(t *time.Time) *FlexibleTime {
	if t == nil {
		return nil
	}
	return &FlexibleTime{Time: *t}
}
