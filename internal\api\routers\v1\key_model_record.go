package router_v1

import (
	"web-api/internal/api/controllers"

	"github.com/gin-gonic/gin"
)

func KeyModelRecordRouter(router *gin.RouterGroup) {
	keyModelRecordGroup := router.Group("key-model-records")
	{
		// CRUD operations
		keyModelRecordGroup.POST("", controllers.KeyModelRecord.Create)
		keyModelRecordGroup.GET("", controllers.KeyModelRecord.GetList)
		keyModelRecordGroup.GET("/paginated", controllers.KeyModelRecord.GetWithPagination)
		keyModelRecordGroup.GET("/distinct", controllers.KeyModelRecord.GetDistinctValues)
		keyModelRecordGroup.GET("/:id", controllers.KeyModelRecord.GetByID)
		keyModelRecordGroup.PUT("/:id", controllers.KeyModelRecord.Update)
		keyModelRecordGroup.DELETE("/:id", controllers.KeyModelRecord.Delete)
	}
}
