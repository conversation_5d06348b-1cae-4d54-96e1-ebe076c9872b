-- =============================================
-- Key Model Tracking Database Schema
-- SQL Server Database Design
-- =============================================

-- Create Database
CREATE DATABASE KeyModelTracking;
GO

USE KeyModelTracking;
GO

-- =============================================
-- Main Tables
-- =============================================

-- 1. Key Model Records (Main table)
CREATE TABLE KeyModelRecords (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ColorwayId NVARCHAR(50) NOT NULL,
    MaterialId NVARCHAR(50) NOT NULL,
    KeyModelType NVARCHAR(100) NOT NULL,
    ProductName NVARCHAR(200) NOT NULL,
    FTY NVARCHAR(100) NOT NULL,
    Quantity INT NULL,
    ISD NVARCHAR(50) NOT NULL,
    Season NVARCHAR(50) NOT NULL,
    POReceivedDate DATETIME2 NULL,
    UpdateDate DATETIME2 NULL,
    POReceived BIT DEFAULT 0,
    PFCValidated BIT DEFAULT 0,
    PTRSSCFMed BIT DEFAULT 0,

-- Design Key Feature
    DesignKeyFeature NVARCHAR(MAX) NULL,
    
    -- Dev Learning
    DevLearning NVARCHAR(MAX) NULL,
    
    -- HR Material Update
    HRMaterialUpdate NVARCHAR(500) NULL,
    
    -- GTM Comment
    GTMComment NVARCHAR(MAX) NULL,


     -- EST UPDATE
    ESTDate DATETIME2 NULL,
    FirstESTPass BIT DEFAULT 0,
    SecondESTPass BIT DEFAULT 0,
    FittingApproval BIT DEFAULT 0,
    PTRSSTested BIT DEFAULT 0,
    
    -- FST UPDATE
    SampleReceivedDate DATETIME2 NULL,
    FSTDate DATETIME2 NULL,
    FirstFSTPass BIT DEFAULT 0,
    SecondFSTPass BIT DEFAULT 0,
    PTRSSTestedFST BIT DEFAULT 0,
    PFCCFMed BIT DEFAULT 0,
    ToolingCFMed BIT DEFAULT 0,
    FSTComment NVARCHAR(MAX) NULL,

    -- PROD MATERIAL UPDATE
    ProdMaterialUpdateDate DATETIME2 NULL,
    PhysicalTestPass BIT DEFAULT 0,
    VisualCheckPass BIT DEFAULT 0,
    ProdMaterialComment NVARCHAR(MAX) NULL,
    
    -- MPPA UPDATE - Process Assessment
    ProcessAssessmentDate DATETIME2 NULL,
    MPPACheckProcess BIT DEFAULT 0,
    MPPAPassProcess BIT DEFAULT 0,
    ProcessComment NVARCHAR(MAX) NULL,
    
    -- MPPA UPDATE - Product Assessment  
    ProductAssessmentDate DATETIME2 NULL,
    MPPACheckProduct BIT DEFAULT 0,
    MPPAPassProduct BIT DEFAULT 0,
    ProductComment NVARCHAR(MAX) NULL,


     CreatedDate DATETIME2 DEFAULT GETDATE(),
    UpdatedDate DATETIME2 DEFAULT GETDATE(),
    
);


