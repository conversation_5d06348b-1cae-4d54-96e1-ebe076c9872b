package router_v1

import (
	"web-api/internal/api/controllers"

	"github.com/gin-gonic/gin"
)

func LookupRouter(router *gin.RouterGroup) {
	// // Season routes
	// seasonGroup := router.Group("seasons")
	// {
	// 	seasonGroup.POST("", controllers.Season.Create)
	// 	seasonGroup.GET("", controllers.Season.GetAll)
	// 	seasonGroup.GET("/:id", controllers.Season.GetByID)
	// 	seasonGroup.PUT("/:id", controllers.Season.Update)
	// 	seasonGroup.DELETE("/:id", controllers.Season.Delete)
	// }

	// // Factory routes
	// factoryGroup := router.Group("factories")
	// {
	// 	factoryGroup.POST("", controllers.Factory.Create)
	// 	factoryGroup.GET("", controllers.Factory.GetAll)
	// 	factoryGroup.GET("/:id", controllers.Factory.GetByID)
	// 	factoryGroup.PUT("/:id", controllers.Factory.Update)
	// 	factoryGroup.DELETE("/:id", controllers.Factory.Delete)
	// }

	// Key Model Type routes
	keyModelTypeGroup := router.Group("key-model-types")
	{
		keyModelTypeGroup.POST("", controllers.KeyModelType.Create)
		keyModelTypeGroup.GET("", controllers.KeyModelType.GetAll)
		keyModelTypeGroup.GET("/:id", controllers.KeyModelType.GetByID)
		keyModelTypeGroup.PUT("/:id", controllers.KeyModelType.Update)
		keyModelTypeGroup.DELETE("/:id", controllers.KeyModelType.Delete)
	}
}
