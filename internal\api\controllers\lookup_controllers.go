package controllers

import (
	"net/http"
	"strconv"
	"web-api/internal/api/services"
	"web-api/internal/pkg/models/request"
	"web-api/internal/pkg/models/response"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// SeasonController handles season operations
type SeasonController struct {
	BaseController
}

var Season = &SeasonController{}

// Create creates a new season
// @Summary Create season
// @Description Create a new season
// @Tags Season
// @Accept json
// @Produce json
// @Param request body request.SeasonRequest true "Season data"
// @Success 200 {object} response.CommonResponse{data=entities.Season}
// @Failure 400 {object} response.CommonResponse
// @Failure 500 {object} response.CommonResponse
// @Router /api/seasons [post]
func (c *SeasonController) Create(ctx *gin.Context) {
	var req request.SeasonRequest
	if err := c.ValidateReqParams(ctx, &req); err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	season, err := services.Season.Create(&req)
	if err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	response.OkWithData(ctx, season)
}

// GetAll retrieves all seasons
// @Summary Get all seasons
// @Description Get all active seasons
// @Tags Season
// @Accept json
// @Produce json
// @Success 200 {object} response.CommonResponse{data=[]entities.Season}
// @Failure 500 {object} response.CommonResponse
// @Router /api/seasons [get]
func (c *SeasonController) GetAll(ctx *gin.Context) {
	seasons, err := services.Season.GetAll()
	if err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	response.OkWithData(ctx, seasons)
}

// GetByID retrieves a season by ID
// @Summary Get season by ID
// @Description Get a season by ID
// @Tags Season
// @Accept json
// @Produce json
// @Param id path int true "Season ID"
// @Success 200 {object} response.CommonResponse{data=entities.Season}
// @Failure 400 {object} response.CommonResponse
// @Failure 404 {object} response.CommonResponse
// @Router /api/seasons/{id} [get]
func (c *SeasonController) GetByID(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid ID format")
		return
	}

	season, err := services.Season.GetByID(id)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusNotFound, nil, err.Error())
		return
	}

	response.OkWithData(ctx, season)
}

// Update updates a season
// @Summary Update season
// @Description Update a season by ID
// @Tags Season
// @Accept json
// @Produce json
// @Param id path int true "Season ID"
// @Param request body request.SeasonRequest true "Update data"
// @Success 200 {object} response.CommonResponse{data=entities.Season}
// @Failure 400 {object} response.CommonResponse
// @Failure 404 {object} response.CommonResponse
// @Router /api/seasons/{id} [put]
func (c *SeasonController) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid ID format")
		return
	}

	var req request.SeasonRequest
	if err := c.ValidateReqParams(ctx, &req); err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	season, err := services.Season.Update(id, &req)
	if err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	response.OkWithData(ctx, season)
}

// Delete deletes a season
// @Summary Delete season
// @Description Soft delete a season by ID
// @Tags Season
// @Accept json
// @Produce json
// @Param id path int true "Season ID"
// @Success 200 {object} response.CommonResponse
// @Failure 400 {object} response.CommonResponse
// @Failure 404 {object} response.CommonResponse
// @Router /api/seasons/{id} [delete]
func (c *SeasonController) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid ID format")
		return
	}

	if err := services.Season.Delete(id); err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	response.OkWithMessage(ctx, "Season deleted successfully")
}

// FactoryController handles factory operations
type FactoryController struct {
	BaseController
}

var Factory = &FactoryController{}

// Create creates a new factory
// @Summary Create factory
// @Description Create a new factory
// @Tags Factory
// @Accept json
// @Produce json
// @Param request body request.FactoryRequest true "Factory data"
// @Success 200 {object} response.CommonResponse{data=entities.Factory}
// @Failure 400 {object} response.CommonResponse
// @Failure 500 {object} response.CommonResponse
// @Router /api/factories [post]
func (c *FactoryController) Create(ctx *gin.Context) {
	var req request.FactoryRequest
	if err := c.ValidateReqParams(ctx, &req); err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	factory, err := services.Factory.Create(&req)
	if err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	response.OkWithData(ctx, factory)
}

// GetAll retrieves all factories
// @Summary Get all factories
// @Description Get all active factories
// @Tags Factory
// @Accept json
// @Produce json
// @Success 200 {object} response.CommonResponse{data=[]entities.Factory}
// @Failure 500 {object} response.CommonResponse
// @Router /api/factories [get]
func (c *FactoryController) GetAll(ctx *gin.Context) {
	factories, err := services.Factory.GetAll()
	if err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	response.OkWithData(ctx, factories)
}

// GetByID retrieves a factory by ID
// @Summary Get factory by ID
// @Description Get a factory by ID
// @Tags Factory
// @Accept json
// @Produce json
// @Param id path int true "Factory ID"
// @Success 200 {object} response.CommonResponse{data=entities.Factory}
// @Failure 400 {object} response.CommonResponse
// @Failure 404 {object} response.CommonResponse
// @Router /api/factories/{id} [get]
func (c *FactoryController) GetByID(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid ID format")
		return
	}

	factory, err := services.Factory.GetByID(id)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusNotFound, nil, err.Error())
		return
	}

	response.OkWithData(ctx, factory)
}

// Update updates a factory
// @Summary Update factory
// @Description Update a factory by ID
// @Tags Factory
// @Accept json
// @Produce json
// @Param id path int true "Factory ID"
// @Param request body request.FactoryRequest true "Update data"
// @Success 200 {object} response.CommonResponse{data=entities.Factory}
// @Failure 400 {object} response.CommonResponse
// @Failure 404 {object} response.CommonResponse
// @Router /api/factories/{id} [put]
func (c *FactoryController) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid ID format")
		return
	}

	var req request.FactoryRequest
	if err := c.ValidateReqParams(ctx, &req); err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	factory, err := services.Factory.Update(id, &req)
	if err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	response.OkWithData(ctx, factory)
}

// Delete deletes a factory
// @Summary Delete factory
// @Description Soft delete a factory by ID
// @Tags Factory
// @Accept json
// @Produce json
// @Param id path int true "Factory ID"
// @Success 200 {object} response.CommonResponse
// @Failure 400 {object} response.CommonResponse
// @Failure 404 {object} response.CommonResponse
// @Router /api/factories/{id} [delete]
func (c *FactoryController) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid ID format")
		return
	}

	if err := services.Factory.Delete(id); err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	response.OkWithMessage(ctx, "Factory deleted successfully")
}

// KeyModelTypeController handles key model type operations
type KeyModelTypeController struct {
	BaseController
}

var KeyModelType = &KeyModelTypeController{}

// Create creates a new key model type
// @Summary Create key model type
// @Description Create a new key model type
// @Tags KeyModelType
// @Accept json
// @Produce json
// @Param request body request.KeyModelTypeRequest true "Key model type data"
// @Success 200 {object} response.CommonResponse{data=entities.KeyModelType}
// @Failure 400 {object} response.CommonResponse
// @Failure 500 {object} response.CommonResponse
// @Router /api/key-model-types [post]
func (c *KeyModelTypeController) Create(ctx *gin.Context) {
	var req request.KeyModelTypeRequest
	if err := c.ValidateReqParams(ctx, &req); err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	keyModelType, err := services.KeyModelType.Create(&req)
	if err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	response.OkWithData(ctx, keyModelType)
}

// GetAll retrieves all key model types
// @Summary Get all key model types
// @Description Get all active key model types
// @Tags KeyModelType
// @Accept json
// @Produce json
// @Success 200 {object} response.CommonResponse{data=[]entities.KeyModelType}
// @Failure 500 {object} response.CommonResponse
// @Router /api/key-model-types [get]
func (c *KeyModelTypeController) GetAll(ctx *gin.Context) {
	keyModelTypes, err := services.KeyModelType.GetAll()
	if err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	response.OkWithData(ctx, keyModelTypes)
}

// GetByID retrieves a key model type by ID
// @Summary Get key model type by ID
// @Description Get a key model type by ID
// @Tags KeyModelType
// @Accept json
// @Produce json
// @Param id path int true "Key model type ID"
// @Success 200 {object} response.CommonResponse{data=entities.KeyModelType}
// @Failure 400 {object} response.CommonResponse
// @Failure 404 {object} response.CommonResponse
// @Router /api/key-model-types/{id} [get]
func (c *KeyModelTypeController) GetByID(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid ID format")
		return
	}

	keyModelType, err := services.KeyModelType.GetByID(id)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusNotFound, nil, err.Error())
		return
	}

	response.OkWithData(ctx, keyModelType)
}

// Update updates a key model type
// @Summary Update key model type
// @Description Update a key model type by ID
// @Tags KeyModelType
// @Accept json
// @Produce json
// @Param id path int true "Key model type ID"
// @Param request body request.KeyModelTypeRequest true "Update data"
// @Success 200 {object} response.CommonResponse{data=entities.KeyModelType}
// @Failure 400 {object} response.CommonResponse
// @Failure 404 {object} response.CommonResponse
// @Router /api/key-model-types/{id} [put]
func (c *KeyModelTypeController) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid ID format")
		return
	}

	var req request.KeyModelTypeRequest
	if err := c.ValidateReqParams(ctx, &req); err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	keyModelType, err := services.KeyModelType.Update(id, &req)
	if err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	response.OkWithData(ctx, keyModelType)
}

// Delete deletes a key model type
// @Summary Delete key model type
// @Description Soft delete a key model type by ID
// @Tags KeyModelType
// @Accept json
// @Produce json
// @Param id path int true "Key model type ID"
// @Success 200 {object} response.CommonResponse
// @Failure 400 {object} response.CommonResponse
// @Failure 404 {object} response.CommonResponse
// @Router /api/key-model-types/{id} [delete]
func (c *KeyModelTypeController) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid ID format")
		return
	}

	if err := services.KeyModelType.Delete(id); err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	response.OkWithMessage(ctx, "Key model type deleted successfully")
}
