stages:
  - build
  - deploy

variables:
  DOCKER_DRIVER: caoverlay2
  SERVICE_NAME: "key-model-tracking-api"
  TAR_FILE: "${SERVICE_NAME}-bak"
  ENV: ".env.production"
  SERVER: *************

build:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  before_script:
    - docker info
  script:
    - docker build -t ${SERVICE_NAME}:latest .
    - docker save -o ${SERVICE_NAME}.tar ${SERVICE_NAME}:latest
  artifacts:
    paths:
      - ${SERVICE_NAME}.tar
  only:
    - main

deploy:
  stage: deploy
  image: docker:latest
  services:
    - docker:dind
  dependencies:
    - build
  before_script:
    - apk add --update --no-cache openssh-client
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY_23_25" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan -H ${SERVER} >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
    - ssh-add -l # kiểm tra agent đã add key chưa
  script:
    - scp ${SERVICE_NAME}.tar root@${SERVER}:/root/Docker/${SERVICE_NAME}.tar
    - ssh root@${SERVER} "docker load -i  /root/Docker/${SERVICE_NAME}.tar"
    - ssh root@${SERVER} "docker compose -f /root/Docker/Nginx/docker-compose.yaml config -q"
    - ssh root@${SERVER} "cd /root/Docker/Nginx && docker compose up -d --no-build --pull=never ${SERVICE_NAME}"
  retry: 1
  only:
    - main
