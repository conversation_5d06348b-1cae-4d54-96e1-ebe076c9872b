# Key Model Tracking API Documentation

## 📋 Overview
This API provides comprehensive CRUD operations for the Key Model Tracking system with a unified data model that consolidates all tracking stages into a single table for optimal performance and data consistency.

### Features
- ✅ **Unified Data Model** - All stages (Product Creation, Commercial, Manufacturing) in one table
- ✅ **Complete CRUD Operations** - Create, Read, Update, Delete for all entities
- ✅ **Advanced Filtering** - Search and filter by multiple criteria
- ✅ **Pagination Support** - Handle large datasets efficiently
- ✅ **Lookup Tables** - Seasons, Factories, Key Model Types management
- ✅ **RESTful Design** - Standard HTTP methods and status codes

**Base URL:** `http://localhost:8004/api/`

---

## 🔐 Authentication
Currently, no authentication is required. All endpoints are publicly accessible.

---

## 📤 Response Format

All API responses follow this standard format:

```json
{
    "code": 200,
    "data": {}, // Response data or null
    "message": "success" // Success/error message
}
```

### HTTP Status Codes
| Code | Description |
|------|-------------|
| `200` | Success |
| `400` | Bad Request (validation errors) |
| `404` | Not Found |
| `500` | Internal Server Error |

---

## 📊 Data Types

### UUID Format
All IDs for main entities use UUID format: `550e8400-e29b-41d4-a716-************`

### Date Format
All dates use ISO 8601 format: `2024-01-15T10:30:00Z`

---

## 🏗️ Data Structure

The Key Model Record contains all stage information in a single unified table:

### Basic Information
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `colorwayId` | string | ✅ | Colorway identifier |
| `materialId` | string | ✅ | Material identifier |
| `keyModelType` | string | ✅ | Type of key model |
| `productName` | string | ✅ | Product name |
| `fty` | string | ✅ | Factory code |
| `quantity` | integer | ❌ | Production quantity |
| `isd` | string | ✅ | ISD identifier |
| `season` | string | ✅ | Season code |
| `poReceivedDate` | datetime | ❌ | PO received date |

### Product Creation Stage (GTMF RFC UPDATE)
| Field | Type | Description |
|-------|------|-------------|
| `updateDate` | datetime | Update date |
| `poReceived` | boolean | PO received status |
| `pfcValidated` | boolean | PFC validation status |
| `ptrssCfmed` | boolean | PTRSS confirmation status |
| `designKeyFeature` | string | Design key features |
| `devLearning` | string | Development learning notes |
| `hrMaterialUpdate` | string | HR material update info |
| `gtmComment` | string | GTM comments |

### Commercial Stage (EST UPDATE)
| Field | Type | Description |
|-------|------|-------------|
| `estDate` | datetime | EST date |
| `firstEstPass` | boolean | First EST pass status |
| `secondEstPass` | boolean | Second EST pass status |
| `fittingApproval` | boolean | Fitting approval status |
| `ptrssTestedEst` | boolean | PTRSS tested for EST |
| `estComment` | string | EST comments |

### Commercial Stage (FST UPDATE)
| Field | Type | Description |
|-------|------|-------------|
| `sampleReceivedDate` | datetime | Sample received date |
| `fstDate` | datetime | FST date |
| `firstFstPass` | boolean | First FST pass status |
| `secondFstPass` | boolean | Second FST pass status |
| `ptrssTestedFst` | boolean | PTRSS tested for FST |
| `pfcCfmed` | boolean | PFC confirmation status |
| `toolingCfmed` | boolean | Tooling confirmation status |
| `fstComment` | string | FST comments |

### Manufacturing Stage (PROD MATERIAL UPDATE)
| Field | Type | Description |
|-------|------|-------------|
| `prodMaterialUpdateDate` | datetime | Production material update date |
| `physicalTestPass` | boolean | Physical test pass status |
| `visualCheckPass` | boolean | Visual check pass status |
| `prodMaterialComment` | string | Production material comments |

### Manufacturing Stage (MPPA UPDATE - Process Assessment)
| Field | Type | Description |
|-------|------|-------------|
| `processAssessmentDate` | datetime | Process assessment date |
| `mppaCheckProcess` | boolean | MPPA process check status |
| `mppaPassProcess` | boolean | MPPA process pass status |
| `processComment` | string | Process comments |

### Manufacturing Stage (MPPA UPDATE - Product Assessment)
| Field | Type | Description |
|-------|------|-------------|
| `productAssessmentDate` | datetime | Product assessment date |
| `mppaCheckProduct` | boolean | MPPA product check status |
| `mppaPassProduct` | boolean | MPPA product pass status |
| `productComment` | string | Product comments |

### User Tracking
| Field | Type | Description |
|-------|------|-------------|
| `userId` | string | User ID who created/modified the record (max 50 characters) |

---

## 🚀 API Endpoints

### Key Model Records

#### 1. Create Key Model Record
**POST** `/api/key-model-records`

Creates a new key model record with all stage information.

**Request Body:**
```json
{
    "colorwayId": "CW001",
    "materialId": "MAT001",
    "keyModelType": "DESIGN",
    "productName": "Nike Air Max 270",
    "fty": "LTN",
    "quantity": 1000,
    "isd": "ISD001",
    "season": "S25",
    "poReceivedDate": "2024-01-15T10:30:00Z",

    // Product Creation Stage (optional)
    "updateDate": "2024-01-16T10:30:00Z",
    "poReceived": true,
    "pfcValidated": false,
    "designKeyFeature": "Advanced cushioning technology",
    "devLearning": "New material testing required",
    "hrMaterialUpdate": "Material specs updated",
    "gtmComment": "Ready for next phase",

    // Commercial Stage - EST (optional)
    "estDate": "2024-01-20T10:30:00Z",
    "firstEstPass": true,
    "fittingApproval": true,
    "ptrssTestedEst": true,
    "estComment": "EST phase completed successfully",

    // Commercial Stage - FST (optional)
    "sampleReceivedDate": "2024-01-18T10:30:00Z",
    "fstDate": "2024-01-22T10:30:00Z",
    "firstFstPass": true,
    "ptrssTestedFst": true,
    "pfcCfmed": true,
    "fstComment": "Sample approved with minor adjustments",

    // Manufacturing Stage - Material (optional)
    "prodMaterialUpdateDate": "2024-01-25T10:30:00Z",
    "physicalTestPass": true,
    "visualCheckPass": true,
    "prodMaterialComment": "All materials approved",

    // Manufacturing Stage - Process (optional)
    "processAssessmentDate": "2024-01-26T10:30:00Z",
    "mppaCheckProcess": true,
    "mppaPassProcess": true,
    "processComment": "Process validated successfully",

    // Manufacturing Stage - Product (optional)
    "productAssessmentDate": "2024-01-27T10:30:00Z",
    "mppaCheckProduct": true,
    "mppaPassProduct": true,
    "productComment": "Product ready for mass production",

    // User tracking (optional)
    "userId": "user123"
}
```

**Response:**
```json
{
    "code": 200,
    "data": {
        "id": "550e8400-e29b-41d4-a716-************",
        "colorwayId": "CW001",
        "materialId": "MAT001",
        "keyModelType": "DESIGN",
        "productName": "Nike Air Max 270",
        "fty": "LTN",
        "quantity": 1000,
        "isd": "ISD001",
        "season": "S25",
        "poReceivedDate": "2024-01-15T10:30:00Z",

        "updateDate": "2024-01-16T10:30:00Z",
        "poReceived": true,
        "pfcValidated": false,
        "ptrssCfmed": false,
        "designKeyFeature": "Advanced cushioning technology",
        "devLearning": "New material testing required",
        "hrMaterialUpdate": "Material specs updated",
        "gtmComment": "Ready for next phase",

        "estDate": "2024-01-20T10:30:00Z",
        "firstEstPass": true,
        "secondEstPass": false,
        "fittingApproval": true,
        "ptrssTestedEst": true,
        "estComment": "EST phase completed successfully",

        "sampleReceivedDate": "2024-01-18T10:30:00Z",
        "fstDate": "2024-01-22T10:30:00Z",
        "firstFstPass": true,
        "secondFstPass": false,
        "ptrssTestedFst": true,
        "pfcCfmed": true,
        "toolingCfmed": false,
        "fstComment": "Sample approved with minor adjustments",

        "prodMaterialUpdateDate": "2024-01-25T10:30:00Z",
        "physicalTestPass": true,
        "visualCheckPass": true,
        "prodMaterialComment": "All materials approved",

        "processAssessmentDate": "2024-01-26T10:30:00Z",
        "mppaCheckProcess": true,
        "mppaPassProcess": true,
        "processComment": "Process validated successfully",

        "productAssessmentDate": "2024-01-27T10:30:00Z",
        "mppaCheckProduct": true,
        "mppaPassProduct": true,
        "productComment": "Product ready for mass production",

        "userId": "user123"
    },
    "message": "success"
}
```

#### 2. Get Key Model Record by ID
**GET** `/api/key-model-records/{id}`

Retrieves a specific key model record with all stage information.

**Parameters:**
- `id` (path) - UUID of the key model record

**Response:**
```json
{
    "code": 200,
    "data": {
        "id": "550e8400-e29b-41d4-a716-************",
        "colorwayId": "CW001",
        "materialId": "MAT001",
        // ... all fields included
    },
    "message": "success"
}
```

#### 3. Update Key Model Record
**PUT** `/api/key-model-records/{id}`

Updates an existing key model record. All fields are optional.

**Parameters:**
- `id` (path) - UUID of the key model record

**Request Body:**
```json
{
    "productName": "Nike Air Max 270 Updated",
    "quantity": 1200,
    "poReceived": true,
    "firstEstPass": true,
    // ... any fields to update
}
```

**Response:**
```json
{
    "code": 200,
    "data": {
        "id": "550e8400-e29b-41d4-a716-************",
        // ... updated record with all fields
    },
    "message": "success"
}
```

#### 4. Delete Key Model Record
**DELETE** `/api/key-model-records/{id}`

Permanently deletes a key model record.

**Parameters:**
- `id` (path) - UUID of the key model record

**Response:**
```json
{
    "code": 200,
    "data": null,
    "message": "Key model record deleted successfully"
}
```

#### 5. Get Key Model Records List
**GET** `/api/key-model-records`

Retrieves a list of key model records with optional filtering.

**Query Parameters:**
| Parameter | Type | Description |
|-----------|------|-------------|
| `colorwayId` | string | Filter by colorway ID (partial match) |
| `materialId` | string | Filter by material ID (partial match) |
| `keyModelType` | string | Filter by key model type (partial match) |
| `productName` | string | Filter by product name (partial match) |
| `isd` | string | Filter by ISD (partial match) |
| `fty` | string | Filter by FTY (partial match) |
| `season` | string | Filter by season (partial match) |

**Example:**
```
GET /api/key-model-records?productName=Nike&season=S25&fty=LTN
```

**Response:**
```json
{
    "code": 200,
    "data": [
        {
            "id": "550e8400-e29b-41d4-a716-************",
            "colorwayId": "CW001",
            "materialId": "MAT001",
            "productName": "Nike Air Max 270",
            // ... all fields
        }
    ],
    "message": "success"
}
```

#### 6. Get Key Model Records with Pagination
**GET** `/api/key-model-records/paginated`

Retrieves key model records with pagination support.

**Query Parameters:**
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `pageNumber` | integer | 1 | Page number (1-based) |
| `pageSize` | integer | 10 | Number of records per page (max: 1000) |
| All filter parameters from above | | | |

**Example:**
```
GET /api/key-model-records/paginated?pageNumber=1&pageSize=20&productName=Nike
```

**Response:**
```json
{
    "code": 200,
    "data": [
        {
            "id": "550e8400-e29b-41d4-a716-************",
            "colorwayId": "CW001",
            "materialId": "MAT001",
            "productName": "Nike Air Max 270",
            // ... all fields
        }
    ],
    "message": "success"
}
```

#### 7. Get Distinct Values
**GET** `/api/key-model-records/distinct`

Retrieves distinct values for specific fields to populate filter dropdowns.

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `field` | string | ✅ | Field name to get distinct values for |

**Valid field values:**
- `colorwayId`
- `materialId`
- `keyModelType`
- `productName`
- `isd`
- `fty`
- `season`

**Example:**
```
GET /api/key-model-records/distinct?field=season
```

**Response:**
```json
{
    "code": 200,
    "data": ["S25", "F24", "W25"],
    "message": "success"
}
```

## 📚 Lookup Tables APIs

### Seasons

#### Get All Seasons
**GET** `/api/seasons`

**Response:**
```json
{
    "code": 200,
    "data": [
        {
            "id": 1,
            "seasonCode": "S25",
            "seasonName": "Spring 2025",
            "isActive": true
        },
        {
            "id": 2,
            "seasonCode": "F24",
            "seasonName": "Fall 2024",
            "isActive": true
        }
    ],
    "message": "success"
}
```

#### Create Season
**POST** `/api/seasons`

**Request Body:**
```json
{
    "seasonCode": "S25",
    "seasonName": "Spring 2025",
    "isActive": true
}
```

#### Update Season
**PUT** `/api/seasons/{id}`

#### Delete Season
**DELETE** `/api/seasons/{id}`

### Factories

#### Get All Factories
**GET** `/api/factories`

**Response:**
```json
{
    "code": 200,
    "data": [
        {
            "id": 1,
            "factoryCode": "LTN",
            "factoryName": "LTN Factory",
            "location": "Vietnam",
            "isActive": true
        }
    ],
    "message": "success"
}
```

#### Create Factory
**POST** `/api/factories`

**Request Body:**
```json
{
    "factoryCode": "LTN",
    "factoryName": "LTN Factory",
    "location": "Vietnam",
    "isActive": true
}
```

#### Update Factory
**PUT** `/api/factories/{id}`

#### Delete Factory
**DELETE** `/api/factories/{id}`

### Key Model Types

#### Get All Key Model Types
**GET** `/api/key-model-types`

**Response:**
```json
{
    "code": 200,
    "data": [
        {
            "id": 1,
            "typeCode": "DESIGN",
            "typeName": "Design Production Complexity",
            "description": "Design and production complexity assessment",
            "isActive": true
        }
    ],
    "message": "success"
}
```

#### Create Key Model Type
**POST** `/api/key-model-types`

**Request Body:**
```json
{
    "typeCode": "DESIGN",
    "typeName": "Design Production Complexity",
    "description": "Design and production complexity assessment",
    "isActive": true
}
```

#### Update Key Model Type
**PUT** `/api/key-model-types/{id}`

#### Delete Key Model Type
**DELETE** `/api/key-model-types/{id}`

---

## ⚠️ Error Handling

### Validation Errors (400)
```json
{
    "code": 400,
    "data": null,
    "message": "Invalid JSON format: Key: 'KeyModelRecordRequest.ColorwayID' Error:Field validation for 'ColorwayID' failed on the 'required' tag"
}
```

### Not Found Errors (404)
```json
{
    "code": 404,
    "data": null,
    "message": "key model record not found"
}
```

### Server Errors (500)
```json
{
    "code": 500,
    "data": null,
    "message": "Internal server error"
}
```

## 💡 Usage Examples

### Complete Workflow Example

#### 1. Create a Key Model Record
```bash
curl -X POST http://localhost:8004/api/key-model-records \
  -H "Content-Type: application/json" \
  -d '{
    "colorwayId": "CW001",
    "materialId": "MAT001",
    "keyModelType": "DESIGN",
    "productName": "Nike Air Max 270",
    "fty": "LTN",
    "quantity": 1000,
    "isd": "ISD001",
    "season": "S25",
    "poReceivedDate": "2024-01-15T10:30:00Z"
  }'
```

#### 2. Update Product Creation Stage
```bash
curl -X PUT http://localhost:8004/api/key-model-records/550e8400-e29b-41d4-a716-************ \
  -H "Content-Type: application/json" \
  -d '{
    "poReceived": true,
    "pfcValidated": true,
    "designKeyFeature": "Advanced cushioning technology",
    "gtmComment": "Ready for EST phase"
  }'
```

#### 3. Update Commercial Stage
```bash
curl -X PUT http://localhost:8004/api/key-model-records/550e8400-e29b-41d4-a716-************ \
  -H "Content-Type: application/json" \
  -d '{
    "estDate": "2024-01-20T10:30:00Z",
    "firstEstPass": true,
    "fittingApproval": true,
    "ptrssTestedEst": true
  }'
```

#### 4. Search Records with Filters
```bash
curl "http://localhost:8004/api/key-model-records/paginated?productName=Nike&pageSize=20&season=S25"
```

#### 5. Get Distinct Values for Dropdowns
```bash
curl "http://localhost:8004/api/key-model-records/distinct?field=season"
curl "http://localhost:8004/api/key-model-records/distinct?field=fty"
```

---

## 🎯 Best Practices

### Frontend Integration Tips

1. **UUID Validation**
   - Always validate UUID format before making API calls
   - Use proper UUID libraries in your frontend framework

2. **Handle Null Values**
   - All optional fields can be `null` or `undefined`
   - Implement proper null checking in your UI components

3. **Error Handling**
   - Implement comprehensive error handling for all API calls
   - Show user-friendly error messages based on error codes

4. **Pagination**
   - Use pagination for large datasets to improve performance
   - Implement proper loading states and pagination controls

5. **Caching Strategy**
   - Cache lookup data (seasons, factories, key model types) as they change infrequently
   - Implement cache invalidation when lookup data is updated

6. **Filter Dropdowns**
   - Use the distinct values endpoint to populate filter dropdowns
   - Implement debounced search for better user experience

7. **Date Handling**
   - Always use ISO 8601 format for dates
   - Handle timezone conversions properly in your frontend

8. **Optimistic Updates**
   - Consider implementing optimistic updates for better UX
   - Always handle rollback scenarios for failed updates

### Performance Considerations

1. **Use Pagination**
   - Always use pagination for list endpoints
   - Set reasonable page sizes (10-50 records per page)

2. **Efficient Filtering**
   - Use specific filters to reduce data transfer
   - Combine multiple filters for more precise results

3. **Batch Operations**
   - For multiple updates, consider implementing batch endpoints
   - Minimize the number of API calls

### Security Notes

1. **Input Validation**
   - Always validate input data on both client and server side
   - Sanitize user inputs to prevent injection attacks

2. **Rate Limiting**
   - Implement proper rate limiting in production
   - Handle rate limit responses gracefully

---

## 📋 Summary

This API provides a comprehensive solution for Key Model Tracking with:

- ✅ **Unified Data Model** - All tracking stages in one table
- ✅ **Complete CRUD Operations** - Full lifecycle management
- ✅ **Advanced Filtering & Search** - Flexible data retrieval
- ✅ **Pagination Support** - Efficient handling of large datasets
- ✅ **Lookup Tables Management** - Reference data maintenance
- ✅ **RESTful Design** - Standard HTTP methods and status codes
- ✅ **Comprehensive Error Handling** - Clear error messages
- ✅ **Production Ready** - Optimized for performance and scalability

**Server Status:** ✅ Running on `http://localhost:8004`

For technical support or questions, please refer to the development team.

---

*Last updated: 2024-01-28*
