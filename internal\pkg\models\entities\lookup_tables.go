package entities

// Season represents the seasons lookup table
type Season struct {
	ID         int    `gorm:"primary_key;auto_increment" json:"id"`
	SeasonCode string `gorm:"type:nvarchar(10);not null;unique" json:"seasonCode" binding:"required"`
	SeasonName string `gorm:"type:nvarchar(50);not null" json:"seasonName" binding:"required"`
	IsActive   bool   `gorm:"type:bit;default:1" json:"isActive"`
}

// TableName specifies the table name for GORM
func (Season) TableName() string {
	return "seasons"
}

// Factory represents the factories lookup table
type Factory struct {
	ID          int     `gorm:"primary_key;auto_increment" json:"id"`
	FactoryCode string  `gorm:"type:nvarchar(20);not null;unique" json:"factoryCode" binding:"required"`
	FactoryName string  `gorm:"type:nvarchar(200);not null" json:"factoryName" binding:"required"`
	Location    *string `gorm:"type:nvarchar(100)" json:"location"`
	IsActive    bool    `gorm:"type:bit;default:1" json:"isActive"`
}

// TableName specifies the table name for GORM
func (Factory) TableName() string {
	return "factories"
}

// KeyModelType represents the key model types lookup table
type KeyModelType struct {
	ID          int     `gorm:"primary_key;auto_increment" json:"id"`
	TypeCode    string  `gorm:"type:nvarchar(20);not null;unique" json:"typeCode" binding:"required"`
	TypeName    string  `gorm:"type:nvarchar(100);not null" json:"typeName" binding:"required"`
	Description *string `gorm:"type:nvarchar(500)" json:"description"`
	IsActive    bool    `gorm:"type:bit;default:1" json:"isActive"`
}

// TableName specifies the table name for GORM
func (KeyModelType) TableName() string {
	return "key_model_types"
}
