package services

import (
	"errors"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/entities"
	"web-api/internal/pkg/models/request"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type KeyModelRecordService struct {
	BaseService
}

var KeyModelRecord = &KeyModelRecordService{}

// Create creates a new key model record
func (s *KeyModelRecordService) Create(req *request.KeyModelRecordRequest) (*entities.KeyModelRecord, error) {
	// Generate standard UUID and convert to SQLServerUUID
	newUUID := uuid.New()
	record := &entities.KeyModelRecord{
		// Basic Information
		Id:             entities.SQLServerUUID(newUUID),
		ColorwayId:     req.ColorwayID,
		MaterialId:     req.MaterialID,
		KeyModelType:   req.KeyModelType,
		ProductName:    req.ProductName,
		FTY:            req.FTY,
		Quantity:       req.Quantity,
		ISD:            req.ISD,
		Season:         req.Season,
		POReceivedDate: req.POReceivedDate.ToTimePointer(),

		// Product Creation Stage (GTMF RFC UPDATE)
		UpdateDate:       req.UpdateDate.ToTimePointer(),
		DesignKeyFeature: req.DesignKeyFeature,
		DevLearning:      req.DevLearning,
		HRMaterialUpdate: req.HRMaterialUpdate,
		GTMComment:       req.GTMComment,

		// Commercial Stage (EST UPDATE)
		ESTDate:    req.ESTDate.ToTimePointer(),
		ESTComment: req.ESTComment,

		// Commercial Stage (FST UPDATE)
		SampleReceivedDate: req.SampleReceivedDate.ToTimePointer(),
		FSTDate:            req.FSTDate.ToTimePointer(),
		FSTComment:         req.FSTComment,

		// Manufacturing Stage (PROD MATERIAL UPDATE)
		ProdMaterialUpdateDate: req.ProdMaterialUpdateDate.ToTimePointer(),
		ProdMaterialComment:    req.ProdMaterialComment,

		// Manufacturing Stage (MPPA UPDATE - Process Assessment)
		ProcessAssessmentDate: req.ProcessAssessmentDate.ToTimePointer(),
		ProcessComment:        req.ProcessComment,

		// Manufacturing Stage (MPPA UPDATE - Product Assessment)
		ProductAssessmentDate: req.ProductAssessmentDate.ToTimePointer(),
		ProductComment:        req.ProductComment,

		// User tracking
		UserID: req.UserID, // Will be nil if not provided
	}

	// Set boolean fields if provided
	if req.POReceived != nil {
		record.POReceived = *req.POReceived
	}
	if req.PFCValidated != nil {
		record.PFCValidated = *req.PFCValidated
	}
	if req.PTRSSCFMed != nil {
		record.PTRSSCFMed = *req.PTRSSCFMed
	}
	if req.FirstESTPass != nil {
		record.FirstESTPass = *req.FirstESTPass
	}
	if req.SecondESTPass != nil {
		record.SecondESTPass = *req.SecondESTPass
	}
	if req.FittingApproval != nil {
		record.FittingApproval = *req.FittingApproval
	}
	if req.PTRSSTested != nil {
		record.PTRSSTested = *req.PTRSSTested
	}
	if req.FirstFSTPass != nil {
		record.FirstFSTPass = *req.FirstFSTPass
	}
	if req.SecondFSTPass != nil {
		record.SecondFSTPass = *req.SecondFSTPass
	}
	if req.PTRSSTestedFST != nil {
		record.PTRSSTestedFST = *req.PTRSSTestedFST
	}
	if req.PFCCFMed != nil {
		record.PFCCFMed = *req.PFCCFMed
	}
	if req.ToolingCFMed != nil {
		record.ToolingCFMed = *req.ToolingCFMed
	}
	if req.PhysicalTestPass != nil {
		record.PhysicalTestPass = *req.PhysicalTestPass
	}
	if req.VisualCheckPass != nil {
		record.VisualCheckPass = *req.VisualCheckPass
	}
	if req.MPPACheckProcess != nil {
		record.MPPACheckProcess = *req.MPPACheckProcess
	}
	if req.MPPAPassProcess != nil {
		record.MPPAPassProcess = *req.MPPAPassProcess
	}
	if req.MPPACheckProduct != nil {
		record.MPPACheckProduct = *req.MPPACheckProduct
	}
	if req.MPPAPassProduct != nil {
		record.MPPAPassProduct = *req.MPPAPassProduct
	}

	if err := database.GetDB().Create(record).Error; err != nil {
		return nil, err
	}

	return record, nil
}

// GetByMaterialId retrieves a key model record by ID
func (s *KeyModelRecordService) GetByID(id uuid.UUID) (*entities.KeyModelRecord, error) {
	var record entities.KeyModelRecord

	err := database.GetDB().
		Where("MaterialId = ?", id).
		First(&record).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("key model record not found")
		}
		return nil, err
	}

	return &record, nil
}

// Update updates a key model record
func (s *KeyModelRecordService) Update(id uuid.UUID, req *request.KeyModelRecordUpdateRequest) (*entities.KeyModelRecord, error) {
	var record entities.KeyModelRecord

	// First check if record exists
	if err := database.GetDB().Where("Id = ?", id).First(&record).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("key model record not found")
		}
		return nil, err
	}

	// Update fields if provided
	updates := make(map[string]interface{})

	// Basic Information
	if req.ColorwayID != nil {
		updates["ColorwayId"] = *req.ColorwayID
	}
	if req.MaterialID != nil {
		updates["MaterialId"] = *req.MaterialID
	}
	if req.KeyModelType != nil {
		updates["KeyModelType"] = *req.KeyModelType
	}
	if req.ProductName != nil {
		updates["ProductName"] = *req.ProductName
	}
	if req.FTY != nil {
		updates["FTY"] = *req.FTY
	}
	if req.Quantity != nil {
		updates["Quantity"] = *req.Quantity
	}
	if req.ISD != nil {
		updates["ISD"] = *req.ISD
	}
	if req.Season != nil {
		updates["Season"] = *req.Season
	}
	if req.POReceivedDate != nil {
		updates["POReceivedDate"] = req.POReceivedDate.ToTimePointer()
	}

	// Product Creation Stage (GTMF RFC UPDATE)
	if req.UpdateDate != nil {
		updates["UpdateDate"] = req.UpdateDate.ToTimePointer()
	}
	if req.POReceived != nil {
		updates["POReceived"] = *req.POReceived
	}
	if req.PFCValidated != nil {
		updates["PFCValidated"] = *req.PFCValidated
	}
	if req.PTRSSCFMed != nil {
		updates["PTRSSCFMed"] = *req.PTRSSCFMed
	}
	if req.DesignKeyFeature != nil {
		updates["DesignKeyFeature"] = *req.DesignKeyFeature
	}
	if req.DevLearning != nil {
		updates["DevLearning"] = *req.DevLearning
	}
	if req.HRMaterialUpdate != nil {
		updates["HRMaterialUpdate"] = *req.HRMaterialUpdate
	}
	if req.GTMComment != nil {
		updates["GTMComment"] = *req.GTMComment
	}

	// Commercial Stage (EST UPDATE)
	if req.ESTDate != nil {
		updates["ESTDate"] = req.ESTDate.ToTimePointer()
	}
	if req.FirstESTPass != nil {
		updates["FirstESTPass"] = *req.FirstESTPass
	}
	if req.SecondESTPass != nil {
		updates["SecondESTPass"] = *req.SecondESTPass
	}
	if req.FittingApproval != nil {
		updates["FittingApproval"] = *req.FittingApproval
	}
	if req.PTRSSTested != nil {
		updates["PTRSSTested"] = *req.PTRSSTested
	}
	if req.ESTComment != nil {
		updates["ESTComment"] = *req.ESTComment
	}

	// Commercial Stage (FST UPDATE)
	if req.SampleReceivedDate != nil {
		updates["SampleReceivedDate"] = req.SampleReceivedDate.ToTimePointer()
	}
	if req.FSTDate != nil {
		updates["FSTDate"] = req.FSTDate.ToTimePointer()
	}
	if req.FirstFSTPass != nil {
		updates["FirstFSTPass"] = *req.FirstFSTPass
	}
	if req.SecondFSTPass != nil {
		updates["SecondFSTPass"] = *req.SecondFSTPass
	}
	if req.PTRSSTestedFST != nil {
		updates["PTRSSTestedFST"] = *req.PTRSSTestedFST
	}
	if req.PFCCFMed != nil {
		updates["PFCCFMed"] = *req.PFCCFMed
	}
	if req.ToolingCFMed != nil {
		updates["ToolingCFMed"] = *req.ToolingCFMed
	}
	if req.FSTComment != nil {
		updates["FSTComment"] = *req.FSTComment
	}

	// Manufacturing Stage (PROD MATERIAL UPDATE)
	if req.ProdMaterialUpdateDate != nil {
		updates["ProdMaterialUpdateDate"] = req.ProdMaterialUpdateDate.ToTimePointer()
	}
	if req.PhysicalTestPass != nil {
		updates["PhysicalTestPass"] = *req.PhysicalTestPass
	}
	if req.VisualCheckPass != nil {
		updates["VisualCheckPass"] = *req.VisualCheckPass
	}
	if req.ProdMaterialComment != nil {
		updates["ProdMaterialComment"] = *req.ProdMaterialComment
	}

	// Manufacturing Stage (MPPA UPDATE - Process Assessment)
	if req.ProcessAssessmentDate != nil {
		updates["ProcessAssessmentDate"] = req.ProcessAssessmentDate.ToTimePointer()
	}
	if req.MPPACheckProcess != nil {
		updates["MPPACheckProcess"] = *req.MPPACheckProcess
	}
	if req.MPPAPassProcess != nil {
		updates["MPPAPassProcess"] = *req.MPPAPassProcess
	}
	if req.ProcessComment != nil {
		updates["ProcessComment"] = *req.ProcessComment
	}

	// Manufacturing Stage (MPPA UPDATE - Product Assessment)
	if req.ProductAssessmentDate != nil {
		updates["ProductAssessmentDate"] = req.ProductAssessmentDate.ToTimePointer()
	}
	if req.MPPACheckProduct != nil {
		updates["MPPACheckProduct"] = *req.MPPACheckProduct
	}
	if req.MPPAPassProduct != nil {
		updates["MPPAPassProduct"] = *req.MPPAPassProduct
	}
	if req.ProductComment != nil {
		updates["ProductComment"] = *req.ProductComment
	}

	// User tracking
	if req.UserID != nil {
		updates["UserID"] = *req.UserID
	}

	if err := database.GetDB().Model(&record).Updates(updates).Error; err != nil {
		return nil, err
	}

	// Reload the record
	return s.GetByID(id)
}

// Delete deletes a key model record
func (s *KeyModelRecordService) Delete(id uuid.UUID) error {
	var record entities.KeyModelRecord

	if err := database.GetDB().Where("Id = ?", id).First(&record).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("key model record not found")
		}
		return err
	}

	return database.GetDB().Delete(&record).Error
}

// GetList retrieves a list of key model records with filtering
func (s *KeyModelRecordService) GetList(filter *request.KeyModelRecordFilterRequest) ([]entities.KeyModelRecord, error) {
	var records []entities.KeyModelRecord

	query := database.GetDB().Model(&entities.KeyModelRecord{})

	// Apply filters
	if filter.ColorwayID != nil {
		query = query.Where("ColorwayId LIKE ?", "%"+*filter.ColorwayID+"%")
	}
	if filter.MaterialID != nil {
		query = query.Where("MaterialId LIKE ?", "%"+*filter.MaterialID+"%")
	}
	if filter.KeyModelType != nil {
		query = query.Where("KeyModelType LIKE ?", "%"+*filter.KeyModelType+"%")
	}
	if filter.ProductName != nil {
		query = query.Where("ProductName LIKE ?", "%"+*filter.ProductName+"%")
	}
	if filter.ISD != nil {
		query = query.Where("ISD LIKE ?", "%"+*filter.ISD+"%")
	}
	if filter.FTY != nil {
		query = query.Where("FTY LIKE ?", "%"+*filter.FTY+"%")
	}
	if filter.Season != nil {
		query = query.Where("Season LIKE ?", "%"+*filter.Season+"%")
	}

	err := query.Order("CreatedDate DESC").Find(&records).Error
	if err != nil {
		return records, err
	}

	return records, err
}

// GetWithPagination retrieves key model records with simple pagination
func (s *KeyModelRecordService) GetWithPagination(filter *request.KeyModelRecordFilterRequest, pageNumber, pageSize int) ([]entities.KeyModelRecord, error) {
	var records []entities.KeyModelRecord

	query := database.GetDB().Model(&entities.KeyModelRecord{})

	// Apply filters (same as GetList)
	if filter.ColorwayID != nil {
		query = query.Where("ColorwayId LIKE ?", "%"+*filter.ColorwayID+"%")
	}
	if filter.MaterialID != nil {
		query = query.Where("MaterialId LIKE ?", "%"+*filter.MaterialID+"%")
	}
	if filter.KeyModelType != nil {
		query = query.Where("KeyModelType LIKE ?", "%"+*filter.KeyModelType+"%")
	}
	if filter.ProductName != nil {
		query = query.Where("ProductName LIKE ?", "%"+*filter.ProductName+"%")
	}
	if filter.ISD != nil {
		query = query.Where("ISD LIKE ?", "%"+*filter.ISD+"%")
	}
	if filter.FTY != nil {
		query = query.Where("FTY LIKE ?", "%"+*filter.FTY+"%")
	}
	if filter.Season != nil {
		query = query.Where("Season LIKE ?", "%"+*filter.Season+"%")
	}

	// Apply pagination
	offset := (pageNumber - 1) * pageSize
	err := query.Order("created_date DESC").
		Limit(pageSize).
		Offset(offset).
		Find(&records).Error

	return records, err
}
