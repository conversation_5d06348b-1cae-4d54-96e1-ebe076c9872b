package router_v1

import (
	"web-api/internal/api/controllers"

	"github.com/gin-gonic/gin"
)

func PingRouter(router *gin.RouterGroup) {
	router.GET("/ping", controllers.Common.Ping)
}

func CommonRouter(router *gin.RouterGroup) {
	router.POST("/login", controllers.Common.Login)

	// Distinct value routes from kfxxzl table
	router.GET("/colorways", controllers.Common.GetDistinctColorways)
	router.GET("/materials", controllers.Common.GetDistinctMaterials)
	router.GET("/seasons", controllers.Common.GetDistinctSeasons)
	router.GET("/factories", controllers.Common.GetDistinctFactories)

	// API trả về tất cả các dòng kfxxzl, hỗ trợ filter và phân trang
	router.GET("/kfxxzl-rows", controllers.Common.GetKfxxzlRowsFiltered)
}
