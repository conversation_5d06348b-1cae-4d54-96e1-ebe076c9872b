# API Documentation: Get Filtered KFXXZL Rows

## Endpoint
`GET /kfxxzl-rows`

## Description
Trả về danh sách các dòng từ bảng `kfxxzl`, hỗ trợ lọc và phân trang.

## Tham số truy vấn (Query Parameters)

| Tên         | Kiểu     | <PERSON>ô tả                                    | Bắt buộc | Ví dụ           |
|-------------|----------|------------------------------------------|----------|-----------------|
| page        | int      | Số trang cho phân trang                  | Không    | 1               |
| page_size   | int      | Số lượng dòng mỗi trang                  | Không    | 20              |
| colorway    | string   | Lọc theo colorway                        | Không    | "Red/White"    |
| material    | string   | Lọc theo material                        | Không    | "Leather"      |
| season      | string   | Lọc theo season                          | Không    | "Summer 2025"  |
| factory     | string   | Lọc theo factory                         | Không    | "Factory A"    |
| ...         | ...      | Các trường lọc khác nếu cần               | Không    |                 |

## Response

- `200 OK`
  Trả về một đối tượng JSON gồm:
  - `data`: Mảng các dòng đã lọc
  - `total`: Tổng số dòng phù hợp
  - `page`: Số trang hiện tại
  - `page_size`: Số lượng dòng mỗi trang

### Ví dụ Request
```
GET /kfxxzl-rows?page=1&page_size=20&colorway=Red/White&season=Summer%202025
```

### Ví dụ Response
```json
{
  "data": [
    {
      "id": 1,
      "colorway": "Red/White",
      "material": "Leather",
      "season": "Summer 2025",
      "factory": "Factory A"
      // ...các trường khác
    }
    // ...nhiều dòng khác
  ],
  "total": 120,
  "page": 1,
  "page_size": 20
}
```

## Ghi chú
- Tất cả các tham số lọc đều không bắt buộc.
- Nếu không truyền tham số phân trang, backend sẽ dùng giá trị mặc định.
