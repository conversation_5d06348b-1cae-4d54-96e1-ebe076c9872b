package controllers

import (
	"net/http"
	"web-api/internal/api/services"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/entities"
	"web-api/internal/pkg/models/request"
	"web-api/internal/pkg/models/response"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

type KeyModelRecordController struct {
	BaseController
}

var KeyModelRecord = &KeyModelRecordController{}

// Create creates a new key model record
// @Summary Create key model record
// @Description Create a new key model record
// @Tags KeyModelRecord
// @Accept json
// @Produce json
// @Param request body request.KeyModelRecordRequest true "Key model record data"
// @Success 200 {object} response.CommonResponse{data=entities.KeyModelRecord}
// @Failure 400 {object} response.CommonResponse
// @Failure 500 {object} response.CommonResponse
// @Router /api/key-model-records [post]
func (c *KeyModelRecordController) Create(ctx *gin.Context) {
	var req request.KeyModelRecordRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid JSON format: "+err.Error())
		return
	}

	record, err := services.KeyModelRecord.Create(&req)
	if err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	response.OkWithData(ctx, record)
}

// GetByID retrieves a key model record by ID
// @Summary Get key model record by ID
// @Description Get a key model record by ID with all related stages
// @Tags KeyModelRecord
// @Accept json
// @Produce json
// @Param id path string true "Key model record ID"
// @Success 200 {object} response.CommonResponse{data=entities.KeyModelRecord}
// @Failure 400 {object} response.CommonResponse
// @Failure 404 {object} response.CommonResponse
// @Router /api/key-model-records/{id} [get]
func (c *KeyModelRecordController) GetByID(ctx *gin.Context) {
	idStr := ctx.Param("MaterialId")
	id, err := uuid.Parse(idStr)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid ID format")
		return
	}

	record, err := services.KeyModelRecord.GetByID(id)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusNotFound, nil, err.Error())
		return
	}

	response.OkWithData(ctx, record)
}

// Update updates a key model record
// @Summary Update key model record
// @Description Update a key model record by ID
// @Tags KeyModelRecord
// @Accept json
// @Produce json
// @Param id path string true "Key model record ID"
// @Param request body request.KeyModelRecordUpdateRequest true "Update data"
// @Success 200 {object} response.CommonResponse{data=entities.KeyModelRecord}
// @Failure 400 {object} response.CommonResponse
// @Failure 404 {object} response.CommonResponse
// @Router /api/key-model-records/{id} [put]
func (c *KeyModelRecordController) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid ID format")
		return
	}

	var req request.KeyModelRecordUpdateRequest
	if err := c.ValidateReqParams(ctx, &req); err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	record, err := services.KeyModelRecord.Update(id, &req)
	if err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	response.OkWithData(ctx, record)
}

// Delete deletes a key model record
// @Summary Delete key model record
// @Description Soft delete a key model record by ID
// @Tags KeyModelRecord
// @Accept json
// @Produce json
// @Param id path string true "Key model record ID"
// @Success 200 {object} response.CommonResponse
// @Failure 400 {object} response.CommonResponse
// @Failure 404 {object} response.CommonResponse
// @Router /api/key-model-records/{id} [delete]
func (c *KeyModelRecordController) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid ID format")
		return
	}

	if err := services.KeyModelRecord.Delete(id); err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	response.OkWithMessage(ctx, "Key model record deleted successfully")
}

// GetList retrieves a list of key model records
// @Summary Get key model records list
// @Description Get a list of key model records with filtering
// @Tags KeyModelRecord
// @Accept json
// @Produce json
// @Param colorwayId query string false "Filter by colorway ID"
// @Param materialId query string false "Filter by material ID"
// @Param keyModelType query string false "Filter by key model type"
// @Param productName query string false "Filter by product name"
// @Param isd query string false "Filter by ISD"
// @Param fty query string false "Filter by FTY"
// @Param season query string false "Filter by season"
// @Param isActive query bool false "Filter by active status"
// @Success 200 {object} response.CommonResponse{data=[]entities.KeyModelRecord}
// @Failure 400 {object} response.CommonResponse
// @Router /api/key-model-records [get]
func (c *KeyModelRecordController) GetList(ctx *gin.Context) {
	var filter request.KeyModelRecordFilterRequest
	if err := ctx.ShouldBindQuery(&filter); err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	records, err := services.KeyModelRecord.GetList(&filter)
	if err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	response.OkWithData(ctx, records)
}

// GetWithPagination retrieves key model records with simple pagination
// @Summary Get key model records with pagination
// @Description Get key model records with simple pagination and filtering
// @Tags KeyModelRecord
// @Accept json
// @Produce json
// @Param colorwayId query string false "Filter by colorway ID"
// @Param materialId query string false "Filter by material ID"
// @Param keyModelType query string false "Filter by key model type"
// @Param productName query string false "Filter by product name"
// @Param isd query string false "Filter by ISD"
// @Param fty query string false "Filter by FTY"
// @Param season query string false "Filter by season"
// @Param isActive query bool false "Filter by active status"
// @Param pageNumber query int false "Page number" default(1)
// @Param pageSize query int false "Page size" default(10)
// @Success 200 {object} response.CommonResponse{data=[]entities.KeyModelRecord}
// @Failure 400 {object} response.CommonResponse
// @Router /api/key-model-records/paginated [get]
func (c *KeyModelRecordController) GetWithPagination(ctx *gin.Context) {
	var filter request.KeyModelRecordFilterRequest
	if err := ctx.ShouldBindQuery(&filter); err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	// Set default pagination values
	pageNumber := filter.PageNumber
	if pageNumber <= 0 {
		pageNumber = 1
	}
	pageSize := filter.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}
	if pageSize > 1000 {
		pageSize = 1000
	}

	records, err := services.KeyModelRecord.GetWithPagination(&filter, pageNumber, pageSize)
	if err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	response.OkWithData(ctx, records)
}

// GetDistinctValues retrieves distinct values for filtering
// @Summary Get distinct values
// @Description Get distinct values for specific fields
// @Tags KeyModelRecord
// @Accept json
// @Produce json
// @Param field query string true "Field name (colorwayId, materialId, keyModelType, productName, isd, fty, season)"
// @Success 200 {object} response.CommonResponse{data=[]string}
// @Failure 400 {object} response.CommonResponse
// @Router /api/key-model-records/distinct [get]
func (c *KeyModelRecordController) GetDistinctValues(ctx *gin.Context) {
	field := ctx.Query("field")
	if field == "" {
		response.FailWithMessage(ctx, "Field parameter is required")
		return
	}

	// Map frontend field names to database column names
	fieldMap := map[string]string{
		"colorwayId":   "ColorwayId",
		"materialId":   "MaterialId",
		"keyModelType": "KeyModelType",
		"productName":  "ProductName",
		"isd":          "ISD",
		"fty":          "FTY",
		"season":       "Season",
	}

	dbField, exists := fieldMap[field]
	if !exists {
		response.FailWithMessage(ctx, "Invalid field name")
		return
	}

	var values []string
	if err := database.GetDB().Model(&entities.KeyModelRecord{}).
		Distinct().
		Pluck(dbField, &values).Error; err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	response.OkWithData(ctx, values)
}
