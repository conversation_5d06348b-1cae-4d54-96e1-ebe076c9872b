package controllers

import (
	"fmt"
	"log"
	"net/http"
	"web-api/internal/api/services"
	"web-api/internal/pkg/config"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/request"
	"web-api/internal/pkg/models/response"
	"web-api/internal/pkg/models/types"

	"github.com/gin-gonic/gin"
)

type CommonController struct {
	*BaseController
}

var Common = &CommonController{}

func (c *CommonController) Ping(ctx *gin.Context) {

	env := config.LoadFileENV()
	db, err := database.DatabaseConnection()
	if err != nil {
		fmt.Println("Connection to "+env.HOST+"  Failed : ", err)
		ctx.JSON(500, gin.H{
			"message": "Connection to " + env.HOST + " Failed",
			"error":   err.Error(),
		})
		return
	}
	dbInstance, _ := db.DB()
	dbInstance.Close()
	ctx.JSON(200, gin.H{
		"message": "Connected to " + env.HOST + " successfully",
	})
}

func (c *CommonController) Login(ctx *gin.Context) {
	var requestParams request.LoginRequest // dùng struct riêng cho login
	if err := ctx.ShouldBindJSON(&requestParams); err != nil {
		log.Printf("Invalid JSON: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid input format")
		return
	}

	result, err := services.Common.Login(&types.User{
		UserID:   requestParams.UserID,
		Password: requestParams.Password,
	})
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusUnauthorized, nil, err.Error())
		return
	}

	response.OkWithData(ctx, result)
}

// GetDistinctColorways retrieves distinct colorway IDs from kfxxzl table
// @Summary Get distinct colorways
// @Description Get all distinct colorway IDs from kfxxzl table
// @Tags Common
// @Accept json
// @Produce json
// @Success 200 {object} response.CommonResponse{data=[]string}
// @Failure 500 {object} response.CommonResponse
// @Router /api/common/colorways [get]
func (c *CommonController) GetDistinctColorways(ctx *gin.Context) {
	colorways, err := services.Common.GetDistinctColorways()
	if err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	response.OkWithData(ctx, colorways)
}

// GetDistinctMaterials retrieves distinct material IDs from kfxxzl table
// @Summary Get distinct materials
// @Description Get all distinct material IDs from kfxxzl table
// @Tags Common
// @Accept json
// @Produce json
// @Success 200 {object} response.CommonResponse{data=[]string}
// @Failure 500 {object} response.CommonResponse
// @Router /api/common/materials [get]
func (c *CommonController) GetDistinctMaterials(ctx *gin.Context) {
	materials, err := services.Common.GetDistinctMaterials()
	if err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	response.OkWithData(ctx, materials)
}

// GetDistinctSeasons retrieves distinct seasons from kfxxzl table
// @Summary Get distinct seasons
// @Description Get all distinct seasons from kfxxzl table
// @Tags Common
// @Accept json
// @Produce json
// @Success 200 {object} response.CommonResponse{data=[]string}
// @Failure 500 {object} response.CommonResponse
// @Router /api/common/seasons [get]
func (c *CommonController) GetDistinctSeasons(ctx *gin.Context) {
	seasons, err := services.Common.GetDistinctSeasons()
	if err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	response.OkWithData(ctx, seasons)
}

// GetDistinctFactories retrieves distinct factories from kfxxzl table
// @Summary Get distinct factories
// @Description Get all distinct factories from kfxxzl table
// @Tags Common
// @Accept json
// @Produce json
// @Success 200 {object} response.CommonResponse{data=[]string}
// @Failure 500 {object} response.CommonResponse
// @Router /api/common/factories [get]
func (c *CommonController) GetDistinctFactories(ctx *gin.Context) {
	factories, err := services.Common.GetDistinctFactories()
	if err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}

	response.OkWithData(ctx, factories)
}

// GetKfxxzlRows returns all rows from kfxxzl with ColorwayId, MaterialId, Season, FTY
// @Summary Get all kfxxzl rows
// @Description Get all rows from kfxxzl with ColorwayId, MaterialId, Season, FTY
// @Tags Common
// @Accept json
// @Produce json
// @Success 200 {object} response.CommonResponse{data=[]response.KfxxzlRow}
// @Failure 500 {object} response.CommonResponse
// @Router /api/kfxxzl-rows [get]
func (c *CommonController) GetKfxxzlRows(ctx *gin.Context) {
	rows, err := services.Common.GetKfxxzlRows()
	if err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}
	response.OkWithData(ctx, rows)
}

// GetKfxxzlRowsFiltered trả về các dòng kfxxzl, filter và phân trang
// @Summary Get filtered kfxxzl rows with pagination
// @Description Get rows from kfxxzl with filter and pagination (ColorwayId, MaterialId, season, factory)
// @Tags Common
// @Accept json
// @Produce json
// @Param colorwayId query string false "ColorwayId"
// @Param materialId query string false "MaterialId"
// @Param season query string false "Season"
// @Param factory query string false "Factory"
// @Param page query int false "Page number (default 1)"
// @Param pageSize query int false "Page size (default 20)"
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} response.CommonResponse
// @Router /api/kfxxzl-rows [get]
func (c *CommonController) GetKfxxzlRowsFiltered(ctx *gin.Context) {
	var req request.KfxxzlFilterRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(ctx, "Invalid query params")
		return
	}
	rows, total, err := services.Common.GetKfxxzlRowsFiltered(&req)
	if err != nil {
		response.FailWithMessage(ctx, err.Error())
		return
	}
	ctx.JSON(200, gin.H{
		"code":     200,
		"message":  "success",
		"data":     rows,
		"total":    total,
		"page":     req.Page,
		"pageSize": req.PageSize,
	})
}
